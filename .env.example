# Configuración para desarrollo local
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=tu_password_local
DB_NAME=VerdeQR

# Configuración de Flask
FLASK_ENV=development
SECRET_KEY=tu_clave_secreta_muy_segura_aqui

# Puerto (Railway usa PORT automáticamente)
PORT=5000

# Para Railway (estas se configuran automáticamente):
# DB_HOST=${{MySQL.MYSQL_HOST}}
# DB_USER=${{MySQL.MYSQL_USER}}
# DB_PASSWORD=${{MySQL.MYSQL_PASSWORD}}
# DB_NAME=${{MySQL.MYSQL_DATABASE}}
# FLASK_ENV=production
