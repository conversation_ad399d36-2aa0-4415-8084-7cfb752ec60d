<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VerdeQR - Un dendrólogo en tu bolsillo</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c3e50, #3498db, #2ecc71);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            color: white;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            max-width: 500px;
            margin: 20px;
        }

        .logo {
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            background: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: #2ecc71;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .subtitle {
            font-size: 1.2rem;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .description {
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.8;
        }

        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(46, 204, 113, 0.4);
            margin: 10px;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.6);
            text-decoration: none;
            color: white;
        }

        .btn.secondary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn.secondary:hover {
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.6);
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            transition: transform 0.3s ease;
        }

        .feature:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .feature-text {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .container {
                padding: 30px 20px;
                margin: 10px;
            }

            h1 {
                font-size: 2rem;
            }

            .subtitle {
                font-size: 1rem;
            }

            .btn {
                padding: 12px 24px;
                font-size: 1rem;
                display: block;
                margin: 10px 0;
            }

            .features {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🌳</div>
        <h1>VerdeQR</h1>
        <p class="subtitle">Un dendrólogo en tu bolsillo</p>
        <p class="description">
            Identifica árboles silvestres al instante mediante códigos QR.
            Descubre la biodiversidad que te rodea y contribuye a la conservación del medio ambiente.
        </p>

        <div>
            <a href="/inicio" class="btn">Explorar Árboles</a>
            <a href="/registro" class="btn secondary">Registrarse</a>
        </div>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">📱</div>
                <div class="feature-text">Escaneo QR</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🔬</div>
                <div class="feature-text">Info Científica</div>
            </div>
            <div class="feature">
                <div class="feature-icon">🌿</div>
                <div class="feature-text">Conservación</div>
            </div>
            <div class="feature">
                <div class="feature-icon">📚</div>
                <div class="feature-text">Educación</div>
            </div>
        </div>
    </div>

    <script>
        // Redirección automática después de 3 segundos (opcional)
        // setTimeout(() => {
        //     window.location.href = '/inicio';
        // }, 3000);
    </script>
</body>
</html>