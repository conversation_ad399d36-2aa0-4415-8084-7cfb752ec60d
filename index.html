<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VerdeQR - Un dendrólogo en tu bolsillo</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50, #3498db, #2ecc71);
            background-size: 400% 400%;
            animation: gradientShift 8s ease infinite;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 50px 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            max-width: 600px;
            margin: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        h1 {
            font-size: 3rem;
            margin-bottom: 15px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #fff, #f0f0f0);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .subtitle {
            font-size: 1.4rem;
            margin-bottom: 25px;
            opacity: 0.9;
            font-weight: 300;
        }

        .description {
            font-size: 1.1rem;
            line-height: 1.7;
            margin-bottom: 40px;
            opacity: 0.85;
            max-width: 500px;
            margin-left: auto;
            margin-right: auto;
        }

        .buttons {
            margin-bottom: 40px;
        }

        .btn {
            display: inline-block;
            padding: 15px 35px;
            margin: 10px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            cursor: pointer;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
            box-shadow: 0 5px 15px rgba(46, 204, 113, 0.4);
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(46, 204, 113, 0.6);
            color: white;
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-3px);
            color: white;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px 15px;
            border-radius: 15px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .feature:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #2ecc71;
        }

        .feature-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .feature-text {
            font-size: 0.85rem;
            opacity: 0.8;
            line-height: 1.4;
        }

        .notice {
            background: rgba(52, 152, 219, 0.2);
            border: 1px solid rgba(52, 152, 219, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            font-size: 0.95rem;
            line-height: 1.5;
        }

        .notice-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #3498db;
        }

        @media (max-width: 768px) {
            .container {
                padding: 40px 25px;
                margin: 15px;
            }

            h1 {
                font-size: 2.5rem;
            }

            .subtitle {
                font-size: 1.2rem;
            }

            .description {
                font-size: 1rem;
            }

            .btn {
                padding: 12px 25px;
                font-size: 1rem;
                display: block;
                margin: 15px auto;
                max-width: 250px;
            }

            .features {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
            }

            .feature {
                padding: 20px 10px;
            }

            .feature-icon {
                font-size: 2rem;
            }
        }

        @media (max-width: 480px) {
            .logo {
                width: 100px;
                height: 100px;
                font-size: 3rem;
            }

            h1 {
                font-size: 2rem;
            }

            .features {
                grid-template-columns: 1fr;
                gap: 12px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🌳</div>
        <h1>VerdeQR</h1>
        <p class="subtitle">Un dendrólogo en tu bolsillo</p>
        <p class="description">
            Identifica árboles silvestres al instante mediante códigos QR. 
            Descubre la biodiversidad que te rodea y contribuye a la conservación del medio ambiente.
        </p>
        
        <div class="buttons">
            <a href="#demo" class="btn btn-primary">Ver Demo</a>
            <a href="#about" class="btn btn-secondary">Conoce Más</a>
        </div>

        <div class="features">
            <div class="feature">
                <div class="feature-icon"><i class="fas fa-qrcode"></i></div>
                <div class="feature-title">Escaneo QR</div>
                <div class="feature-text">Identifica árboles al instante</div>
            </div>
            <div class="feature">
                <div class="feature-icon"><i class="fas fa-microscope"></i></div>
                <div class="feature-title">Info Científica</div>
                <div class="feature-text">Datos detallados de especies</div>
            </div>
            <div class="feature">
                <div class="feature-icon"><i class="fas fa-leaf"></i></div>
                <div class="feature-title">Conservación</div>
                <div class="feature-text">Protege la biodiversidad</div>
            </div>
            <div class="feature">
                <div class="feature-icon"><i class="fas fa-graduation-cap"></i></div>
                <div class="feature-title">Educación</div>
                <div class="feature-text">Aprende sobre la naturaleza</div>
            </div>
        </div>

        <div class="notice">
            <div class="notice-title">🚀 Aplicación en Desarrollo</div>
            <p>Esta es una página de presentación estática. La aplicación completa con funcionalidades de registro, base de datos y escaneo QR está disponible en el repositorio de GitHub.</p>
            <p style="margin-top: 10px;">
                <strong>Para usar la aplicación completa:</strong> Clona el repositorio y ejecuta localmente con Flask.
            </p>
        </div>
    </div>

    <script>
        // Animación suave para los botones
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                if (this.getAttribute('href').startsWith('#')) {
                    e.preventDefault();
                    // Aquí puedes agregar más funcionalidad si necesitas
                }
            });
        });

        // Efecto de parallax suave
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.container');
            const speed = scrolled * 0.5;
            parallax.style.transform = `translateY(${speed}px)`;
        });
    </script>
</body>
</html>
